// Fill out your copyright notice in the Description page of Project Settings.

#include "JsWatermarkWidget.h"
#include "Action/JyAbleAbilityComponent.h"
#include "Components/RichTextBlock.h"
#include "Config/Settings/JYGameVersionSetting.h"
#include "Core/Gameplay/JYGameInstance.h"
#include "Utilities/JYGitVersionUtils.h"

void UJsWatermarkWidget::NativeConstruct()
{
	Super::NativeConstruct();

	FString BuildText;
	FString BranchText;
	bool bUseConfigVersion = false;

	// 首先尝试从配置文件获取版本信息
	if(UJYGameInstance* GameInstance = UJYGameInstance::Get(this))
	{
		if (const auto VersionSetting = GameInstance->GetGameVersionSetting())
		{
			// 检查配置文件中的SHA信息是否有效（不是默认占位符）
			// 注意：配置文件中的占位符可能带引号，如 "EngineSHA" 或不带引号 EngineSHA
			bool bValidEngineSHA = !VersionSetting->EngineSHA.Equals(TEXT("EngineSHA")) &&
								   !VersionSetting->EngineSHA.Equals(TEXT("\"EngineSHA\"")) &&
								   !VersionSetting->EngineSHA.IsEmpty();
			bool bValidClientSHA = !VersionSetting->ClientSHA.Equals(TEXT("ClientSHA")) &&
								   !VersionSetting->ClientSHA.Equals(TEXT("\"ClientSHA\"")) &&
								   !VersionSetting->ClientSHA.IsEmpty();
			bool bValidContentSHA = !VersionSetting->ContentSHA.Equals(TEXT("ContentSHA")) &&
									!VersionSetting->ContentSHA.Equals(TEXT("\"ContentSHA\"")) &&
									!VersionSetting->ContentSHA.IsEmpty();

			UE_LOG(LogTemp, Warning, TEXT("JsWatermarkWidget: Config values - EngineSHA:'%s', ClientSHA:'%s', ContentSHA:'%s'"),
				*VersionSetting->EngineSHA, *VersionSetting->ClientSHA, *VersionSetting->ContentSHA);
			UE_LOG(LogTemp, Warning, TEXT("JsWatermarkWidget: Config SHA validation - Engine:%s, Client:%s, Content:%s"),
				bValidEngineSHA ? TEXT("Valid") : TEXT("Invalid"),
				bValidClientSHA ? TEXT("Valid") : TEXT("Invalid"),
				bValidContentSHA ? TEXT("Valid") : TEXT("Invalid"));

			// 只有当至少有一个SHA值有效时，才使用配置文件版本
			if (bValidEngineSHA || bValidClientSHA || bValidContentSHA)
			{
				// 构建新格式的版本信息显示
				FString BranchLine = FString::Printf(TEXT("Branch:%s,%s,%s"),
					*VersionSetting->EngineBranch, *VersionSetting->ClientBranch, *VersionSetting->ContentBranch);

				FString SHALine = FString::Printf(TEXT("SHA:%s,%s,%s"),
					*VersionSetting->EngineSHA, *VersionSetting->ClientSHA, *VersionSetting->ContentSHA);

				// 配置文件版本没有时间信息，显示为N/A
				FString TimeLine = FString::Printf(TEXT("Time:%s,%s,%s"),
					TEXT("N/A"), TEXT("N/A"), TEXT("N/A"));

				// 将详细信息放在左下角（BuildText），并添加版本号
				FString VersionLine = FString::Printf(TEXT("Version:%d.%d.%d(%d)"),
					VersionSetting->MajorVersion, VersionSetting->MinorVersion, VersionSetting->FixVersion, VersionSetting->BuildNo);

				// 配置文件版本默认为源码仓库，不显示Tag
				BuildText = BranchLine + TEXT("\n") + SHALine + TEXT("\n") + TimeLine + TEXT("\n") + VersionLine;

				// BranchText留空
				BranchText = TEXT("");
				bUseConfigVersion = true;
			}
		}
	}

	// 如果配置文件信息无效，且启用了本地Git功能，则尝试从本地git获取
	UE_LOG(LogTemp, Warning, TEXT("JsWatermarkWidget: bUseConfigVersion=%s, IsLocalGitVersionEnabled=%s"),
		bUseConfigVersion ? TEXT("true") : TEXT("false"),
		UJYGitVersionUtils::IsLocalGitVersionEnabled() ? TEXT("true") : TEXT("false"));

	if (!bUseConfigVersion && UJYGitVersionUtils::IsLocalGitVersionEnabled())
	{
		UE_LOG(LogTemp, Warning, TEXT("JsWatermarkWidget: Attempting to get local git version info"));
		FJYGitVersionInfo GitInfo = UJYGitVersionUtils::GetGitVersionInfo();
		if (GitInfo.bIsValid)
		{
			// 获取各仓库信息
			FString EngineBranch = GitInfo.EngineInfo.bIsValid ? GitInfo.EngineInfo.BranchName : TEXT("Unknown");
			FString ClientBranch = GitInfo.ClientInfo.bIsValid ? GitInfo.ClientInfo.BranchName : TEXT("Unknown");
			FString ContentBranch = GitInfo.ContentInfo.bIsValid ? GitInfo.ContentInfo.BranchName : TEXT("Unknown");

			FString EngineSHA = GitInfo.EngineInfo.bIsValid ? GitInfo.EngineInfo.ShortSHA : TEXT("Unknown");
			FString ClientSHA = GitInfo.ClientInfo.bIsValid ? GitInfo.ClientInfo.ShortSHA : TEXT("Unknown");
			FString ContentSHA = GitInfo.ContentInfo.bIsValid ? GitInfo.ContentInfo.ShortSHA : TEXT("Unknown");

			FString EngineTag = GitInfo.EngineInfo.bIsValid ? GitInfo.EngineInfo.CurrentTag : TEXT("Unknown");
			FString ClientTag = GitInfo.ClientInfo.bIsValid ? GitInfo.ClientInfo.CurrentTag : TEXT("Unknown");
			FString ContentTag = GitInfo.ContentInfo.bIsValid ? GitInfo.ContentInfo.CurrentTag : TEXT("Unknown");

			FString EngineTime = GitInfo.EngineInfo.bIsValid ? GitInfo.EngineInfo.LastCommitTimestamp : TEXT("Unknown");
			FString ClientTime = GitInfo.ClientInfo.bIsValid ? GitInfo.ClientInfo.LastCommitTimestamp : TEXT("Unknown");
			FString ContentTime = GitInfo.ContentInfo.bIsValid ? GitInfo.ContentInfo.LastCommitTimestamp : TEXT("Unknown");

			// 检查是否有未提交更改
			bool bHasChanges = (GitInfo.EngineInfo.bIsValid && GitInfo.EngineInfo.bHasUncommittedChanges) ||
							   (GitInfo.ClientInfo.bIsValid && GitInfo.ClientInfo.bHasUncommittedChanges) ||
							   (GitInfo.ContentInfo.bIsValid && GitInfo.ContentInfo.bHasUncommittedChanges);

			FString StatusSuffix = bHasChanges ? TEXT(" (Modified)") : TEXT("");

			// 构建完整的版本信息显示
			FString BranchLine = FString::Printf(TEXT("Branch:%s,%s,%s"),
				*EngineBranch, *ClientBranch, *ContentBranch);

			FString SHALine = FString::Printf(TEXT("SHA:%s,%s,%s"),
				*EngineSHA, *ClientSHA, *ContentSHA);

			FString TimeLine = FString::Printf(TEXT("Time:%s,%s,%s"),
				*EngineTime, *ClientTime, *ContentTime);

			// 判断当前是源码仓库还是Binaries仓库
			// 源码仓库不显示Tag信息，Binaries仓库显示Tag信息
			bool bIsBinariesRepo = UJYGitVersionUtils::IsBinariesEnvironment();

			if (bIsBinariesRepo)
			{
				// Binaries仓库：显示Tag信息
				FString TagLine = FString::Printf(TEXT("Tag:%s,%s,%s"),
					*EngineTag, *ClientTag, *ContentTag);
				BuildText = BranchLine + TEXT("\n") + SHALine + TEXT("\n") + TagLine + TEXT("\n") + TimeLine;
			}
			else
			{
				// 源码仓库：不显示Tag信息
				BuildText = BranchLine + TEXT("\n") + SHALine + TEXT("\n") + TimeLine;
			}

			// BranchText留空
			BranchText = TEXT("");
		}
		else
		{
			// 如果git信息也获取失败，显示默认信息
			BuildText = TEXT("Version info unavailable");
			BranchText = TEXT("Git info unavailable");
		}
	}
	else if (!bUseConfigVersion)
	{
		// 配置文件无效但本地Git功能被禁用
		BuildText = TEXT("Version info unavailable (Local Git disabled)");
		BranchText = TEXT("Use 'JY.EnableLocalGitVersion 1' to enable local Git info");
	}

	// 设置显示文本
	if (BuildInfo.Get())
	{
		BuildInfo.Get()->SetText(FText::FromString(BuildText));
	}
	if (BranchInfo.Get())
	{
		BranchInfo.Get()->SetText(FText::FromString(BranchText));
	}
}

void UJsWatermarkWidget::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	{
		const FDateTime Now = FDateTime::UtcNow() + FTimespan(8, 0, 0);
		Timestamp.Get()->SetText(FText::FromString(
			FString::Printf(TEXT("%s [%3llu]"), *Now.ToString(TEXT("%Y.%m.%d-%H:%M:%S:%s")), GFrameCounter % 1000)
		));
	}
}

void UJsWatermarkWidget::ReceiveAbilityExecuteLog(const uint64 UniqueID, const FString& Log)
{
	AbilityExecuteLogMap.FindOrAdd(UniqueID) = Log;
}
