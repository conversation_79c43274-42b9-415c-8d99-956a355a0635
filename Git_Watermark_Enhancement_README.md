# Git版本信息水印增强功能

## 概述
本次修改为SRC编辑器的水印功能添加了本地Git版本信息获取支持，并将显示格式更新为指定的格式。

## 修改内容

### 1. JYGitVersionUtils.h 和 JYGitVersionUtils.cpp
- **新增功能**: 添加了`GetCurrentTag()`方法，用于获取当前分支所在的Tag信息
- **结构体更新**: 在`FJYSingleRepoGitInfo`结构体中添加了`CurrentTag`字段
- **Tag获取逻辑**: 
  - 首先尝试获取当前HEAD指向的确切tag (`git describe --exact-match --tags HEAD`)
  - 如果没有确切的tag，尝试获取最近的tag (`git describe --tags --abbrev=0`)
  - 如果都没有，返回"Unknown"

### 2. JsWatermarkWidget.cpp
- **显示格式更新**: 将水印显示格式改为指定的格式：
  ```
  Branch:a,b,c
  SHA:1,2,3
  Tag:q,w,e
  Time:z,x,c
  ```
- **仓库类型区分**: 
  - 源码仓库（Engine）：Tag显示为"N/A"（因为源码仓库不需要Tag）
  - 编辑器仓库（Client/Content）：显示实际的Tag信息
- **配置文件版本支持**: 同时支持从配置文件读取的版本信息，对于没有的信息显示"N/A"

### 3. JYGitVersionUtilsTest.cpp
- **测试更新**: 在测试函数中添加了Tag信息的测试和显示

## 显示格式说明

### 本地Git版本信息格式
```
Branch:main,develop,feature-branch
SHA:abc1234,def5678,ghi9012
Tag:N/A,v1.2.3,v2.0.0
Time:2024-01-15 10:30:00,2024-01-14 15:45:00,2024-01-13 09:20:00
```

### 配置文件版本信息格式
```
Branch:engine-branch,client-branch,content-branch
SHA:engine-sha,client-sha,content-sha
Tag:N/A,N/A,N/A
Time:N/A,N/A,N/A
```

## 使用方法

### 启用/禁用本地Git功能
使用控制台命令：
```
JY.EnableLocalGitVersion 1  // 启用
JY.EnableLocalGitVersion 0  // 禁用
```

### 测试Git版本功能
使用控制台命令：
```
JY.TestGitVersion
```

## 工作流程

1. **优先级**: 首先尝试从配置文件获取版本信息
2. **回退机制**: 如果配置文件信息无效且启用了本地Git功能，则从本地Git获取
3. **错误处理**: 如果Git不可用或获取失败，显示相应的错误信息
4. **修改状态**: 如果有未提交的更改，会在显示中添加"(Modified)"标记

## 编译问题修复

### 问题描述
在编译过程中遇到了UE反射系统相关的错误，主要包括：
- 缺少类型说明符错误
- GENERATED_BODY相关错误
- StaticStruct函数重复定义错误

### 解决方案
1. **修复头文件包含顺序**: 将`#include "JYGitVersionUtils.generated.h"`移到头文件开始处
2. **使用正确的UE宏**:
   - 对于有自定义构造函数的UCLASS，使用`GENERATED_UCLASS_BODY()`
   - 恢复构造函数`UJYGitVersionUtils(const FObjectInitializer& ObjectInitializer)`
3. **删除过时的生成文件**: 删除`JYGitVersionUtils.gen.cpp`和`JYGitVersionUtils.generated.h`让UE重新生成

### 修复后的文件结构
```cpp
// JYGitVersionUtils.h
#pragma once
#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "JYGitVersionUtils.generated.h"  // 必须在这里包含

UCLASS()
class JYFRAMEWORK_API UJYGitVersionUtils : public UBlueprintFunctionLibrary
{
    GENERATED_UCLASS_BODY()  // 使用这个宏因为有自定义构造函数
    // ...
};
```

## 注意事项

- Engine仓库的Tag始终显示为"N/A"，因为源码仓库不需要Tag信息
- Client和Content仓库会显示实际的Tag信息（如果有的话）
- 时间格式使用Git的默认时间戳格式
- 所有信息都会在左下角的水印中显示
- 如果遇到编译错误，可能需要删除Intermediate目录下的生成文件让UE重新生成
