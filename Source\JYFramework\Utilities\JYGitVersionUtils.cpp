#include "JYGitVersionUtils.h"
#include "HAL/PlatformProcess.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "HAL/IConsoleManager.h"

// 控制台变量：是否启用本地Git版本信息
static TAutoConsoleVariable<bool> CVarEnableLocalGitVersion(
	TEXT("JY.EnableLocalGitVersion"),
	true,
	TEXT("Enable local Git version information when config file version is invalid.\n")
	TEXT("0: Disabled, 1: Enabled (default)"),
	ECVF_Default
);

UJYGitVersionUtils::UJYGitVersionUtils(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
}

FJYGitVersionInfo UJYGitVersionUtils::GetGitVersionInfo()
{
	FJYGitVersionInfo GitInfo;

	if (!IsGitAvailable())
	{
		UE_LOG(LogTemp, Warning, TEXT("Git is not available on this system"));
		return GitInfo;
	}

	// 判断当前是SRC还是BIN环境
	bool bIsBinariesEnvironment = IsBinariesEnvironment();

	// 获取三个仓库的信息
	GitInfo.EngineInfo = GetSingleRepoGitInfo(GetEngineRepositoryPath(bIsBinariesEnvironment));
	GitInfo.ClientInfo = GetSingleRepoGitInfo(GetClientRepositoryPath(bIsBinariesEnvironment));
	GitInfo.ContentInfo = GetSingleRepoGitInfo(GetContentRepositoryPath(bIsBinariesEnvironment));

	// 如果至少有一个仓库的信息有效，则认为整体有效
	GitInfo.bIsValid = GitInfo.EngineInfo.bIsValid || GitInfo.ClientInfo.bIsValid || GitInfo.ContentInfo.bIsValid;

	return GitInfo;
}

FJYSingleRepoGitInfo UJYGitVersionUtils::GetSingleRepoGitInfo(const FString& RepositoryPath)
{
	FJYSingleRepoGitInfo GitInfo;

	if (!IsGitAvailable())
	{
		return GitInfo;
	}

	FString NormalizedPath = GetNormalizedRepositoryPath(RepositoryPath);

	// 获取分支名
	GitInfo.BranchName = GetCurrentBranch(NormalizedPath);

	// 获取提交SHA
	GitInfo.CommitSHA = GetCurrentCommitSHA(NormalizedPath, false);
	GitInfo.ShortSHA = GetCurrentCommitSHA(NormalizedPath, true);

	// 检查未提交更改
	GitInfo.bHasUncommittedChanges = HasUncommittedChanges(NormalizedPath);

	// 获取最后提交信息
	GetLastCommitInfo(NormalizedPath, GitInfo.LastCommitAuthor, GitInfo.LastCommitTimestamp, GitInfo.LastCommitMessage);

	// 获取当前Tag信息
	GitInfo.CurrentTag = GetCurrentTag(NormalizedPath);

	// 如果至少获取到了基本信息，则认为有效
	GitInfo.bIsValid = !GitInfo.BranchName.Equals(TEXT("Unknown")) && !GitInfo.CommitSHA.Equals(TEXT("Unknown"));

	return GitInfo;
}

FString UJYGitVersionUtils::GetCurrentBranch(const FString& RepositoryPath)
{
	FString Result, Error;
	if (ExecuteGitCommand(TEXT("rev-parse --abbrev-ref HEAD"), RepositoryPath, Result, Error))
	{
		Result.TrimStartAndEndInline();
		return Result.IsEmpty() ? TEXT("Unknown") : Result;
	}
	return TEXT("Unknown");
}

FString UJYGitVersionUtils::GetCurrentCommitSHA(const FString& RepositoryPath, bool bShortSHA)
{
	FString Command = bShortSHA ? TEXT("rev-parse --short HEAD") : TEXT("rev-parse HEAD");
	FString Result, Error;
	if (ExecuteGitCommand(Command, RepositoryPath, Result, Error))
	{
		Result.TrimStartAndEndInline();
		return Result.IsEmpty() ? TEXT("Unknown") : Result;
	}
	return TEXT("Unknown");
}

bool UJYGitVersionUtils::HasUncommittedChanges(const FString& RepositoryPath)
{
	FString Result, Error;
	if (ExecuteGitCommand(TEXT("status --porcelain"), RepositoryPath, Result, Error))
	{
		Result.TrimStartAndEndInline();
		return !Result.IsEmpty();
	}
	return false;
}

bool UJYGitVersionUtils::GetLastCommitInfo(const FString& RepositoryPath, FString& OutAuthor, FString& OutTimestamp, FString& OutMessage)
{
	// 获取作者
	FString Result, Error;
	if (ExecuteGitCommand(TEXT("log -1 --pretty=format:%an"), RepositoryPath, Result, Error))
	{
		OutAuthor = Result.TrimStartAndEnd();
	}
	else
	{
		OutAuthor = TEXT("Unknown");
	}

	// 获取时间戳（不包含时区信息）
	if (ExecuteGitCommand(TEXT("log -1 --pretty=format:%ci"), RepositoryPath, Result, Error))
	{
		FString Timestamp = Result.TrimStartAndEnd();

		// 去掉时区信息（格式: "2024-01-15 10:30:00 +0800" -> "2024-01-15 10:30:00"）
		int32 SpaceIndex = INDEX_NONE;
		if (Timestamp.FindLastChar(' ', SpaceIndex) && SpaceIndex > 0)
		{
			// 检查最后一个空格后是否为时区信息（+或-开头）
			FString TimezonePart = Timestamp.Mid(SpaceIndex + 1);
			if (TimezonePart.StartsWith(TEXT("+")) || TimezonePart.StartsWith(TEXT("-")))
			{
				Timestamp = Timestamp.Left(SpaceIndex);
			}
		}

		OutTimestamp = Timestamp;
	}
	else
	{
		OutTimestamp = TEXT("Unknown");
	}

	// 获取提交消息
	if (ExecuteGitCommand(TEXT("log -1 --pretty=format:%s"), RepositoryPath, Result, Error))
	{
		OutMessage = Result.TrimStartAndEnd();
	}
	else
	{
		OutMessage = TEXT("Unknown");
	}

	return !OutAuthor.Equals(TEXT("Unknown")) && !OutTimestamp.Equals(TEXT("Unknown"));
}

bool UJYGitVersionUtils::IsBinariesEnvironment()
{
	// 方法1: 检查可执行文件路径是否包含Binaries
	FString ExecutablePath = FPlatformProcess::ExecutablePath();
	if (ExecutablePath.Contains(TEXT("Binaries")))
	{
		return true;
	}

	// 方法2: 检查是否为打包版本
	if (FPlatformProperties::RequiresCookedData())
	{
		return true;
	}

	// 方法3: 检查项目路径特征
	FString ProjectDir = FPaths::ProjectDir();
	if (ProjectDir.Contains(TEXT("Binaries")))
	{
		return true;
	}

	// 方法4: 检查远程仓库URL（如果可以获取的话）
	FString Result, Error;
	if (ExecuteGitCommand(TEXT("remote get-url origin"), TEXT(""), Result, Error))
	{
		if (Result.Contains(TEXT("/Binaries/")))
		{
			return true;
		}
	}

	// 默认认为是源码环境
	return false;
}

FString UJYGitVersionUtils::GetCurrentTag(const FString& RepositoryPath)
{
	FString Result, Error;

	// 首先尝试获取当前HEAD指向的确切tag
	if (ExecuteGitCommand(TEXT("describe --exact-match --tags HEAD"), RepositoryPath, Result, Error))
	{
		Result.TrimStartAndEndInline();
		if (!Result.IsEmpty())
		{
			return Result;
		}
	}

	// 如果没有确切的tag，尝试获取最近的tag
	if (ExecuteGitCommand(TEXT("describe --tags --abbrev=0"), RepositoryPath, Result, Error))
	{
		Result.TrimStartAndEndInline();
		if (!Result.IsEmpty())
		{
			return Result;
		}
	}

	return TEXT("Unknown");
}

bool UJYGitVersionUtils::ExecuteGitCommand(const FString& GitCommand, const FString& RepositoryPath, FString& OutResult, FString& OutError)
{
	FString FullCommand = FString::Printf(TEXT("git %s"), *GitCommand);
	
	// 设置工作目录
	FString WorkingDirectory = GetNormalizedRepositoryPath(RepositoryPath);
	
	int32 ReturnCode = -1;
	FPlatformProcess::ExecProcess(
		TEXT("git"),
		*GitCommand,
		&ReturnCode,
		&OutResult,
		&OutError,
		*WorkingDirectory
	);

	if (ReturnCode == 0)
	{
		return true;
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Git command failed: %s, Error: %s"), *FullCommand, *OutError);
		return false;
	}
}

FString UJYGitVersionUtils::GetNormalizedRepositoryPath(const FString& RepositoryPath)
{
	if (RepositoryPath.IsEmpty())
	{
		// 使用项目根目录
		return FPaths::ProjectDir();
	}
	
	FString NormalizedPath = RepositoryPath;
	FPaths::NormalizeDirectoryName(NormalizedPath);
	
	// 确保路径存在
	if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*NormalizedPath))
	{
		UE_LOG(LogTemp, Warning, TEXT("Repository path does not exist: %s"), *NormalizedPath);
		return FPaths::ProjectDir();
	}
	
	return NormalizedPath;
}

bool UJYGitVersionUtils::IsGitAvailable()
{
	FString Result, Error;
	int32 ReturnCode = -1;

	FPlatformProcess::ExecProcess(
		TEXT("git"),
		TEXT("--version"),
		&ReturnCode,
		&Result,
		&Error
	);

	return ReturnCode == 0;
}

bool UJYGitVersionUtils::IsLocalGitVersionEnabled()
{
	// 检查控制台变量
	static IConsoleVariable* CVarEnableLocalGit = IConsoleManager::Get().FindConsoleVariable(TEXT("JY.EnableLocalGitVersion"));
	if (CVarEnableLocalGit)
	{
		return CVarEnableLocalGit->GetBool();
	}

	// 默认启用
	return true;
}

FString UJYGitVersionUtils::GetEngineRepositoryPath(bool bIsBinaries)
{
	FString ProjectDir = FPaths::ProjectDir();

	// 检查项目路径是否包含 "UnrealEngine\Games" 或 "UnrealEngine/Games"
	if (ProjectDir.Contains(TEXT("UnrealEngine\\Games")) || ProjectDir.Contains(TEXT("UnrealEngine/Games")))
	{
		// 找到UnrealEngine的位置
		int32 UnrealEngineIndex = ProjectDir.Find(TEXT("UnrealEngine"));
		if (UnrealEngineIndex != INDEX_NONE)
		{
			// 截取到UnrealEngine目录
			FString EnginePath = ProjectDir.Left(UnrealEngineIndex + 13); // "UnrealEngine".Len() = 13

			// 检查这个路径是否存在.git
			FString GitPath = FPaths::Combine(EnginePath, TEXT(".git"));
			if (FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*GitPath) ||
				FPlatformFileManager::Get().GetPlatformFile().FileExists(*GitPath))
			{
				// 验证这是正确的仓库类型
				FString Result, Error;
				if (ExecuteGitCommand(TEXT("remote get-url origin"), EnginePath, Result, Error))
				{
					bool bIsCorrectRepo = bIsBinaries ? Result.Contains(TEXT("/Binaries/UnrealEngine.git")) :
														Result.Contains(TEXT("/Source/UnrealEngine.git"));
					if (bIsCorrectRepo)
					{
						UE_LOG(LogTemp, Log, TEXT("Found %s Engine repository at: %s"),
							bIsBinaries ? TEXT("Binaries") : TEXT("Source"), *EnginePath);
						return EnginePath;
					}
				}

				// 如果URL验证失败，但路径存在，仍然返回（可能是本地仓库）
				UE_LOG(LogTemp, Log, TEXT("Found Engine repository at: %s"), *EnginePath);
				return EnginePath;
			}
		}
	}

	// 如果找不到，返回项目目录作为fallback
	UE_LOG(LogTemp, Warning, TEXT("Engine repository path not found, using project directory"));
	return ProjectDir;
}

FString UJYGitVersionUtils::GetClientRepositoryPath(bool bIsBinaries)
{
	FString ProjectDir = FPaths::ProjectDir();

	// 验证这是正确的仓库类型
	FString Result, Error;
	if (ExecuteGitCommand(TEXT("remote get-url origin"), ProjectDir, Result, Error))
	{
		bool bIsCorrectRepo = bIsBinaries ? Result.Contains(TEXT("/Binaries/JyGame.git")) :
											Result.Contains(TEXT("/Source/JyGame.git"));
		if (bIsCorrectRepo)
		{
			UE_LOG(LogTemp, Log, TEXT("Found %s Client repository at: %s"),
				bIsBinaries ? TEXT("Binaries") : TEXT("Source"), *ProjectDir);
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Client repository type mismatch. Expected %s, got: %s"),
				bIsBinaries ? TEXT("Binaries") : TEXT("Source"), *Result);
		}
	}

	// 返回项目根目录
	return ProjectDir;
}

FString UJYGitVersionUtils::GetContentRepositoryPath(bool bIsBinaries)
{
	// Content仓库通常是项目的Content目录
	FString ContentPath = FPaths::ProjectContentDir();

	// 检查Content目录是否是独立的git仓库
	FString GitPath = FPaths::Combine(ContentPath, TEXT(".git"));
	if (FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*GitPath) ||
		FPlatformFileManager::Get().GetPlatformFile().FileExists(*GitPath))
	{
		// Content仓库在SRC和BIN环境中是相同的，都指向Develop/Content.git
		// 验证这是正确的仓库
		FString Result, Error;
		if (ExecuteGitCommand(TEXT("remote get-url origin"), ContentPath, Result, Error))
		{
			if (Result.Contains(TEXT("/Develop/Content.git")))
			{
				UE_LOG(LogTemp, Log, TEXT("Found Content repository at: %s"), *ContentPath);
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("Content repository URL unexpected: %s"), *Result);
			}
		}

		return ContentPath;
	}

	// 如果Content不是独立仓库，返回项目目录
	UE_LOG(LogTemp, Warning, TEXT("Content repository not found as separate repo, using project directory"));
	return FPaths::ProjectDir();
}
