#include "JYGitVersionUtils.h"
#include "HAL/PlatformProcess.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "HAL/IConsoleManager.h"
#include "Misc/ConfigCacheIni.h"

// 控制台变量：是否启用本地Git版本信息
static TAutoConsoleVariable<bool> CVarEnableLocalGitVersion(
	TEXT("JY.EnableLocalGitVersion"),
	true,
	TEXT("Enable local Git version information when config file version is invalid.\n")
	TEXT("0: Disabled, 1: Enabled (default)"),
	ECVF_Default
);

// 控制台变量：版本信息显示模式
static TAutoConsoleVariable<int32> CVarVersionDisplayMode(
	TEXT("JY.VersionDisplayMode"),
	0,
	TEXT("Version information display mode:\n")
	TEXT("0: Auto (SRC uses local git, WIN/BIN uses ini file)\n")
	TEXT("1: Force local git mode\n")
	TEXT("2: Force ini file mode (WIN package mode)\n")
	TEXT("3: Force ini file mode (BIN editor mode)"),
	ECVF_Default
);

UJYGitVersionUtils::UJYGitVersionUtils(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
}

FJYGitVersionInfo UJYGitVersionUtils::GetGitVersionInfo()
{
	FJYGitVersionInfo GitInfo;

	// 获取当前显示模式
	EJYVersionDisplayMode DisplayMode = GetVersionDisplayMode();

	// 根据显示模式决定获取方式
	bool bUseLocalGit = false;
	bool bIsBinMode = false;

	switch (DisplayMode)
	{
	case EJYVersionDisplayMode::Auto:
		// 自动模式：判断当前环境
		bUseLocalGit = !IsBinariesEnvironment() && IsGitAvailable() && IsLocalGitVersionEnabled();
		bIsBinMode = IsBinariesEnvironment();
		break;
	case EJYVersionDisplayMode::ForceLocalGit:
		// 强制本地git模式
		bUseLocalGit = IsGitAvailable() && IsLocalGitVersionEnabled();
		bIsBinMode = false;
		break;
	case EJYVersionDisplayMode::ForceIniWin:
		// 强制ini文件模式（WIN包）
		bUseLocalGit = false;
		bIsBinMode = false;
		break;
	case EJYVersionDisplayMode::ForceIniBin:
		// 强制ini文件模式（BIN编辑器）
		bUseLocalGit = false;
		bIsBinMode = true;
		break;
	}

	if (bUseLocalGit)
	{
		// 使用本地git获取版本信息
		UE_LOG(LogTemp, Log, TEXT("Using local git to get version info"));

		if (!IsGitAvailable())
		{
			UE_LOG(LogTemp, Warning, TEXT("Git is not available on this system"));
			return GitInfo;
		}

		// 判断当前是SRC还是BIN环境
		bool bIsBinariesEnvironment = IsBinariesEnvironment();

		// 获取三个仓库的信息
		GitInfo.EngineInfo = GetSingleRepoGitInfo(GetEngineRepositoryPath(bIsBinariesEnvironment));
		GitInfo.ClientInfo = GetSingleRepoGitInfo(GetClientRepositoryPath(bIsBinariesEnvironment));
		GitInfo.ContentInfo = GetSingleRepoGitInfo(GetContentRepositoryPath(bIsBinariesEnvironment));

		// 如果至少有一个仓库的信息有效，则认为整体有效
		GitInfo.bIsValid = GitInfo.EngineInfo.bIsValid || GitInfo.ClientInfo.bIsValid || GitInfo.ContentInfo.bIsValid;
	}
	else
	{
		// 使用ini文件获取版本信息
		UE_LOG(LogTemp, Log, TEXT("Using ini file to get version info (BIN mode: %s)"), bIsBinMode ? TEXT("true") : TEXT("false"));
		GitInfo = GetVersionInfoFromIni();

		// 根据是否为BIN模式调整Tag显示
		if (!bIsBinMode)
		{
			// WIN包模式：所有仓库都不需要显示Tag，将Unknown设置为N/A
			if (GitInfo.EngineInfo.CurrentTag.Equals(TEXT("Unknown")))
			{
				GitInfo.EngineInfo.CurrentTag = TEXT("N/A");
			}
			if (GitInfo.ClientInfo.CurrentTag.Equals(TEXT("Unknown")))
			{
				GitInfo.ClientInfo.CurrentTag = TEXT("N/A");
			}
			if (GitInfo.ContentInfo.CurrentTag.Equals(TEXT("Unknown")))
			{
				GitInfo.ContentInfo.CurrentTag = TEXT("N/A");
			}
		}
		// BIN编辑器模式：使用ini文件中的实际Tag信息（所有仓库都显示实际Tag或Unknown）
	}

	return GitInfo;
}

FJYSingleRepoGitInfo UJYGitVersionUtils::GetSingleRepoGitInfo(const FString& RepositoryPath)
{
	FJYSingleRepoGitInfo GitInfo;

	if (!IsGitAvailable())
	{
		return GitInfo;
	}

	// 检查仓库路径是否为空
	if (RepositoryPath.IsEmpty())
	{
		UE_LOG(LogTemp, Warning, TEXT("GetSingleRepoGitInfo: Repository path is empty"));
		return GitInfo;
	}

	FString NormalizedPath = GetNormalizedRepositoryPath(RepositoryPath);

	// 获取分支名
	GitInfo.BranchName = GetCurrentBranch(NormalizedPath);

	// 获取提交SHA
	GitInfo.CommitSHA = GetCurrentCommitSHA(NormalizedPath, false);
	GitInfo.ShortSHA = GetCurrentCommitSHA(NormalizedPath, true);

	// 检查未提交更改
	GitInfo.bHasUncommittedChanges = HasUncommittedChanges(NormalizedPath);

	// 获取最后提交信息
	GetLastCommitInfo(NormalizedPath, GitInfo.LastCommitAuthor, GitInfo.LastCommitTimestamp, GitInfo.LastCommitMessage);

	// 获取当前Tag信息
	GitInfo.CurrentTag = GetCurrentTag(NormalizedPath);

	// 如果至少获取到了基本信息，则认为有效
	GitInfo.bIsValid = !GitInfo.BranchName.Equals(TEXT("Unknown")) && !GitInfo.CommitSHA.Equals(TEXT("Unknown"));

	return GitInfo;
}

FString UJYGitVersionUtils::GetCurrentBranch(const FString& RepositoryPath)
{
	FString Result, Error;
	if (ExecuteGitCommand(TEXT("rev-parse --abbrev-ref HEAD"), RepositoryPath, Result, Error))
	{
		Result.TrimStartAndEndInline();
		return Result.IsEmpty() ? TEXT("Unknown") : Result;
	}
	return TEXT("Unknown");
}

FString UJYGitVersionUtils::GetCurrentCommitSHA(const FString& RepositoryPath, bool bShortSHA)
{
	FString Command = bShortSHA ? TEXT("rev-parse --short HEAD") : TEXT("rev-parse HEAD");
	FString Result, Error;
	if (ExecuteGitCommand(Command, RepositoryPath, Result, Error))
	{
		Result.TrimStartAndEndInline();
		return Result.IsEmpty() ? TEXT("Unknown") : Result;
	}
	return TEXT("Unknown");
}

bool UJYGitVersionUtils::HasUncommittedChanges(const FString& RepositoryPath)
{
	FString Result, Error;
	if (ExecuteGitCommand(TEXT("status --porcelain"), RepositoryPath, Result, Error))
	{
		Result.TrimStartAndEndInline();
		return !Result.IsEmpty();
	}
	return false;
}

bool UJYGitVersionUtils::GetLastCommitInfo(const FString& RepositoryPath, FString& OutAuthor, FString& OutTimestamp, FString& OutMessage)
{
	// 获取作者
	FString Result, Error;
	if (ExecuteGitCommand(TEXT("log -1 --pretty=format:%an"), RepositoryPath, Result, Error))
	{
		OutAuthor = Result.TrimStartAndEnd();
	}
	else
	{
		OutAuthor = TEXT("Unknown");
	}

	// 获取时间戳（不包含时区信息）
	if (ExecuteGitCommand(TEXT("log -1 --pretty=format:%ci"), RepositoryPath, Result, Error))
	{
		FString Timestamp = Result.TrimStartAndEnd();

		// 去掉时区信息（格式: "2024-01-15 10:30:00 +0800" -> "2024-01-15 10:30:00"）
		int32 SpaceIndex = INDEX_NONE;
		if (Timestamp.FindLastChar(' ', SpaceIndex) && SpaceIndex > 0)
		{
			// 检查最后一个空格后是否为时区信息（+或-开头）
			FString TimezonePart = Timestamp.Mid(SpaceIndex + 1);
			if (TimezonePart.StartsWith(TEXT("+")) || TimezonePart.StartsWith(TEXT("-")))
			{
				Timestamp = Timestamp.Left(SpaceIndex);
			}
		}

		OutTimestamp = Timestamp;
	}
	else
	{
		OutTimestamp = TEXT("Unknown");
	}

	// 获取提交消息
	if (ExecuteGitCommand(TEXT("log -1 --pretty=format:%s"), RepositoryPath, Result, Error))
	{
		OutMessage = Result.TrimStartAndEnd();
	}
	else
	{
		OutMessage = TEXT("Unknown");
	}

	return !OutAuthor.Equals(TEXT("Unknown")) && !OutTimestamp.Equals(TEXT("Unknown"));
}

bool UJYGitVersionUtils::IsBinariesEnvironment()
{
	// 方法1: 检查可执行文件路径是否包含Binaries
	FString ExecutablePath = FPlatformProcess::ExecutablePath();
	if (ExecutablePath.Contains(TEXT("Binaries")))
	{
		return true;
	}

	// 方法2: 检查是否为打包版本
	if (FPlatformProperties::RequiresCookedData())
	{
		return true;
	}

	// 方法3: 检查项目路径特征
	FString ProjectDir = FPaths::ProjectDir();
	if (ProjectDir.Contains(TEXT("Binaries")))
	{
		return true;
	}

	// 方法4: 检查远程仓库URL（如果可以获取的话）
	FString Result, Error;
	if (ExecuteGitCommand(TEXT("remote get-url origin"), TEXT(""), Result, Error))
	{
		if (Result.Contains(TEXT("/Binaries/")))
		{
			return true;
		}
	}

	// 默认认为是源码环境
	return false;
}

FString UJYGitVersionUtils::GetCurrentTag(const FString& RepositoryPath)
{
	FString Result, Error;

	// 首先尝试获取当前HEAD指向的确切tag
	if (ExecuteGitCommand(TEXT("describe --exact-match --tags HEAD"), RepositoryPath, Result, Error))
	{
		Result.TrimStartAndEndInline();
		if (!Result.IsEmpty())
		{
			return Result;
		}
	}

	// 如果没有确切的tag，尝试获取最近的tag
	if (ExecuteGitCommand(TEXT("describe --tags --abbrev=0"), RepositoryPath, Result, Error))
	{
		Result.TrimStartAndEndInline();
		if (!Result.IsEmpty())
		{
			return Result;
		}
	}

	return TEXT("Unknown");
}

bool UJYGitVersionUtils::ExecuteGitCommand(const FString& GitCommand, const FString& RepositoryPath, FString& OutResult, FString& OutError)
{
	FString FullCommand = FString::Printf(TEXT("git %s"), *GitCommand);
	
	// 设置工作目录
	FString WorkingDirectory = GetNormalizedRepositoryPath(RepositoryPath);
	
	int32 ReturnCode = -1;
	FPlatformProcess::ExecProcess(
		TEXT("git"),
		*GitCommand,
		&ReturnCode,
		&OutResult,
		&OutError,
		*WorkingDirectory
	);

	if (ReturnCode == 0)
	{
		return true;
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Git command failed: %s, Error: %s"), *FullCommand, *OutError);
		return false;
	}
}

FString UJYGitVersionUtils::GetNormalizedRepositoryPath(const FString& RepositoryPath)
{
	if (RepositoryPath.IsEmpty())
	{
		// 使用项目根目录
		return FPaths::ProjectDir();
	}
	
	FString NormalizedPath = RepositoryPath;
	FPaths::NormalizeDirectoryName(NormalizedPath);
	
	// 确保路径存在
	if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*NormalizedPath))
	{
		UE_LOG(LogTemp, Warning, TEXT("Repository path does not exist: %s"), *NormalizedPath);
		return FPaths::ProjectDir();
	}
	
	return NormalizedPath;
}

bool UJYGitVersionUtils::IsGitAvailable()
{
	FString Result, Error;
	int32 ReturnCode = -1;

	FPlatformProcess::ExecProcess(
		TEXT("git"),
		TEXT("--version"),
		&ReturnCode,
		&Result,
		&Error
	);

	return ReturnCode == 0;
}

bool UJYGitVersionUtils::IsLocalGitVersionEnabled()
{
	// 检查控制台变量
	static IConsoleVariable* CVarEnableLocalGit = IConsoleManager::Get().FindConsoleVariable(TEXT("JY.EnableLocalGitVersion"));
	if (CVarEnableLocalGit)
	{
		return CVarEnableLocalGit->GetBool();
	}

	// 默认启用
	return true;
}

EJYVersionDisplayMode UJYGitVersionUtils::GetVersionDisplayMode()
{
	// 检查控制台变量
	static IConsoleVariable* CVarDisplayMode = IConsoleManager::Get().FindConsoleVariable(TEXT("JY.VersionDisplayMode"));
	if (CVarDisplayMode)
	{
		int32 ModeValue = CVarDisplayMode->GetInt();
		return static_cast<EJYVersionDisplayMode>(FMath::Clamp(ModeValue, 0, 3));
	}

	// 默认自动模式
	return EJYVersionDisplayMode::Auto;
}

FJYGitVersionInfo UJYGitVersionUtils::GetVersionInfoFromIni()
{
	FJYGitVersionInfo GitInfo;

	// 获取DefaultGameVersion.ini文件路径
	FString GameVersionIniPath = FPaths::Combine(FPaths::ProjectConfigDir(), TEXT("DefaultGameVersion.ini"));

	// 检查文件是否存在
	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*GameVersionIniPath))
	{
		UE_LOG(LogTemp, Warning, TEXT("DefaultGameVersion.ini not found at: %s"), *GameVersionIniPath);
		return GitInfo;
	}

	// 读取ini文件配置
	FConfigFile GameVersionConfig;
	GameVersionConfig.Read(GameVersionIniPath);

	// 从ini文件读取版本信息
	const FString SectionName = TEXT("/Script/JYFramework.JYGameVersionSetting");
	const FConfigSection* Section = GameVersionConfig.FindSection(SectionName);
	if (!Section)
	{
		UE_LOG(LogTemp, Warning, TEXT("Section [%s] not found in DefaultGameVersion.ini"), *SectionName);
		return GitInfo;
	}

	// 辅助函数：从Section中获取字符串值
	auto GetStringValue = [Section](const FString& Key) -> FString
	{
		const FConfigValue* Value = Section->Find(FName(*Key));
		return Value ? Value->GetValue() : TEXT("Unknown");
	};

	// 填充Engine信息
	GitInfo.EngineInfo.BranchName = GetStringValue(TEXT("EngineBranch"));
	GitInfo.EngineInfo.ShortSHA = GetStringValue(TEXT("EngineSHA"));
	GitInfo.EngineInfo.CommitSHA = GitInfo.EngineInfo.ShortSHA; // ini中只有短SHA
	GitInfo.EngineInfo.CurrentTag = GetStringValue(TEXT("EngineTag")); // 从ini读取Tag信息
	GitInfo.EngineInfo.LastCommitTimestamp = TEXT("Unknown"); // ini中没有时间信息
	GitInfo.EngineInfo.LastCommitAuthor = TEXT("Unknown");
	GitInfo.EngineInfo.LastCommitMessage = TEXT("Unknown");
	GitInfo.EngineInfo.bHasUncommittedChanges = false; // ini模式下认为没有未提交更改
	GitInfo.EngineInfo.bIsValid = !GitInfo.EngineInfo.BranchName.Equals(TEXT("Unknown")) &&
								  !GitInfo.EngineInfo.ShortSHA.Equals(TEXT("Unknown"));

	// 填充Client信息
	GitInfo.ClientInfo.BranchName = GetStringValue(TEXT("ClientBranch"));
	GitInfo.ClientInfo.ShortSHA = GetStringValue(TEXT("ClientSHA"));
	GitInfo.ClientInfo.CommitSHA = GitInfo.ClientInfo.ShortSHA; // ini中只有短SHA
	GitInfo.ClientInfo.CurrentTag = GetStringValue(TEXT("ClientTag")); // 从ini读取Tag信息
	GitInfo.ClientInfo.LastCommitTimestamp = TEXT("Unknown");
	GitInfo.ClientInfo.LastCommitAuthor = TEXT("Unknown");
	GitInfo.ClientInfo.LastCommitMessage = TEXT("Unknown");
	GitInfo.ClientInfo.bHasUncommittedChanges = false;
	GitInfo.ClientInfo.bIsValid = !GitInfo.ClientInfo.BranchName.Equals(TEXT("Unknown")) &&
								  !GitInfo.ClientInfo.ShortSHA.Equals(TEXT("Unknown"));

	// 填充Content信息
	GitInfo.ContentInfo.BranchName = GetStringValue(TEXT("ContentBranch"));
	GitInfo.ContentInfo.ShortSHA = GetStringValue(TEXT("ContentSHA"));
	GitInfo.ContentInfo.CommitSHA = GitInfo.ContentInfo.ShortSHA; // ini中只有短SHA
	GitInfo.ContentInfo.CurrentTag = GetStringValue(TEXT("ContentTag")); // 从ini读取Tag信息
	GitInfo.ContentInfo.LastCommitTimestamp = TEXT("Unknown");
	GitInfo.ContentInfo.LastCommitAuthor = TEXT("Unknown");
	GitInfo.ContentInfo.LastCommitMessage = TEXT("Unknown");
	GitInfo.ContentInfo.bHasUncommittedChanges = false;
	GitInfo.ContentInfo.bIsValid = !GitInfo.ContentInfo.BranchName.Equals(TEXT("Unknown")) &&
								   !GitInfo.ContentInfo.ShortSHA.Equals(TEXT("Unknown"));

	// 整体有效性
	GitInfo.bIsValid = GitInfo.EngineInfo.bIsValid || GitInfo.ClientInfo.bIsValid || GitInfo.ContentInfo.bIsValid;

	UE_LOG(LogTemp, Log, TEXT("Successfully loaded version info from ini file"));
	return GitInfo;
}

FString UJYGitVersionUtils::GetEngineRepositoryPath(bool bIsBinaries)
{
	FString ProjectDir = FPaths::ProjectDir();
	UE_LOG(LogTemp, Warning, TEXT("GetEngineRepositoryPath: ProjectDir = %s"), *ProjectDir);

	// 根据项目结构，Engine仓库应该在项目目录的上两级
	// 项目路径: H:\SRC\UnrealEngine\Games\JYGame
	// Engine路径: H:\SRC\UnrealEngine
	FString EnginePath = FPaths::Combine(ProjectDir, TEXT("../.."));
	EnginePath = FPaths::ConvertRelativePathToFull(EnginePath);
	FPaths::NormalizeDirectoryName(EnginePath);

	UE_LOG(LogTemp, Warning, TEXT("GetEngineRepositoryPath: Calculated EnginePath = %s"), *EnginePath);

	// 检查这个路径是否存在.git
	FString GitPath = FPaths::Combine(EnginePath, TEXT(".git"));
	bool bGitExists = FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*GitPath) ||
					  FPlatformFileManager::Get().GetPlatformFile().FileExists(*GitPath);
	UE_LOG(LogTemp, Warning, TEXT("GetEngineRepositoryPath: GitPath = %s, Exists = %s"), *GitPath, bGitExists ? TEXT("true") : TEXT("false"));

	if (bGitExists)
	{
		// 验证这是正确的仓库类型
		FString Result, Error;
		if (ExecuteGitCommand(TEXT("remote get-url origin"), EnginePath, Result, Error))
		{
			UE_LOG(LogTemp, Warning, TEXT("GetEngineRepositoryPath: Remote URL = %s"), *Result);
			bool bIsCorrectRepo = bIsBinaries ? Result.Contains(TEXT("/Binaries/UnrealEngine.git")) :
												Result.Contains(TEXT("/Source/UnrealEngine.git"));
			if (bIsCorrectRepo)
			{
				UE_LOG(LogTemp, Log, TEXT("Found %s Engine repository at: %s"),
					bIsBinaries ? TEXT("Binaries") : TEXT("Source"), *EnginePath);
				return EnginePath;
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("Engine repository URL mismatch. Expected %s, got: %s"),
					bIsBinaries ? TEXT("Binaries") : TEXT("Source"), *Result);
			}
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Failed to get remote URL for Engine repository: %s"), *Error);
		}

		// 如果URL验证失败，但路径存在，仍然返回（可能是本地仓库）
		UE_LOG(LogTemp, Log, TEXT("Found Engine repository at: %s (URL validation failed)"), *EnginePath);
		return EnginePath;
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("GetEngineRepositoryPath: .git not found at calculated path"));
	}

	// 如果找不到，返回空字符串而不是项目目录，避免混淆
	UE_LOG(LogTemp, Error, TEXT("Engine repository path not found! This will cause incorrect SHA display."));
	return TEXT("");
}

FString UJYGitVersionUtils::GetClientRepositoryPath(bool bIsBinaries)
{
	FString ProjectDir = FPaths::ProjectDir();

	// 验证这是正确的仓库类型
	FString Result, Error;
	if (ExecuteGitCommand(TEXT("remote get-url origin"), ProjectDir, Result, Error))
	{
		bool bIsCorrectRepo = bIsBinaries ? Result.Contains(TEXT("/Binaries/JyGame.git")) :
											Result.Contains(TEXT("/Source/JyGame.git"));
		if (bIsCorrectRepo)
		{
			UE_LOG(LogTemp, Log, TEXT("Found %s Client repository at: %s"),
				bIsBinaries ? TEXT("Binaries") : TEXT("Source"), *ProjectDir);
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Client repository type mismatch. Expected %s, got: %s"),
				bIsBinaries ? TEXT("Binaries") : TEXT("Source"), *Result);
		}
	}

	// 返回项目根目录
	return ProjectDir;
}

FString UJYGitVersionUtils::GetContentRepositoryPath(bool bIsBinaries)
{
	// Content仓库通常是项目的Content目录
	FString ContentPath = FPaths::ProjectContentDir();

	// 检查Content目录是否是独立的git仓库
	FString GitPath = FPaths::Combine(ContentPath, TEXT(".git"));
	if (FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*GitPath) ||
		FPlatformFileManager::Get().GetPlatformFile().FileExists(*GitPath))
	{
		// Content仓库在SRC和BIN环境中是相同的，都指向Develop/Content.git
		// 验证这是正确的仓库
		FString Result, Error;
		if (ExecuteGitCommand(TEXT("remote get-url origin"), ContentPath, Result, Error))
		{
			if (Result.Contains(TEXT("/Develop/Content.git")))
			{
				UE_LOG(LogTemp, Log, TEXT("Found Content repository at: %s"), *ContentPath);
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("Content repository URL unexpected: %s"), *Result);
			}
		}

		return ContentPath;
	}

	// 如果Content不是独立仓库，返回项目目录
	UE_LOG(LogTemp, Warning, TEXT("Content repository not found as separate repo, using project directory"));
	return FPaths::ProjectDir();
}
