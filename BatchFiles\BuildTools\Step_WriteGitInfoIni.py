import os
import sys

from BuildConfig import JY_EngineSrcDir, JY_ClientContentDir, JY_ClientSrcDir, JY_EngineBranch, JY_ClientBranch, JY_ContentBranch, JY_AppVersion, JY_VersionBuildNo, JY_MajorVersion, JY_MinorVersion, JY_FixVersion, JY_BuildNo


os.chdir(JY_EngineSrcDir)
engine_head_sha = os.popen('git log -1 --pretty=format:%h').read().strip()
# Engine仓库是源码仓库，不需要Tag
engine_tag = "N/A"

os.chdir(JY_ClientContentDir)
content_head_sha = os.popen('git log -1 --pretty=format:%h').read().strip()
# 获取Content仓库的Tag信息
content_tag = os.popen('git describe --exact-match --tags HEAD 2>/dev/null || git describe --tags --abbrev=0 2>/dev/null || echo "Unknown"').read().strip()

os.chdir(JY_ClientSrcDir)
client_head_sha = os.popen('git log -1 --pretty=format:%h').read().strip()
# 获取Client仓库的Tag信息
client_tag = os.popen('git describe --exact-match --tags HEAD 2>/dev/null || git describe --tags --abbrev=0 2>/dev/null || echo "Unknown"').read().strip()
#if not os.path.exists(os.path.join(JY_RootWorkDir, 'result')):
#    os.makedirs(os.path.join(JY_RootWorkDir, 'result'))
# with open(os.path.join(JY_RootWorkDir, 'result', 'build_data.txt'), 'w') as file:
#     file.write("-------------Content  library file change details----------------------------------------------------------------\n")

#     os.chdir(JY_ClientContentDir)
#     file.write(os.popen('git diff --name-only HEAD~3..HEAD').read())

#     file.write("-------------Client  library file change details----------------------------------------------------------------\n")
#     os.chdir(JY_ClientSrcDir)
#     file.write(os.popen('git diff --name-only HEAD~3..HEAD').read())
if os.path.exists(os.path.join(JY_ClientSrcDir, 'Config', 'DefaultGameVersion.ini')):
        os.remove(os.path.join(JY_ClientSrcDir, 'Config', 'DefaultGameVersion.ini'))
with open(os.path.join(JY_ClientSrcDir, 'Config', 'DefaultGameVersion.ini'), 'a') as file:
    # file.write('\n')
    # file.write('[/Script/JYBuildInfo.GitInfo]\n')
    # file.write(f'.JY_EngineBranch={JY_EngineBranch}\n')
    # file.write(f'.JY_EngineHeadSha={engine_head_sha}\n')
    # file.write(f'.JY_ClientBranch={JY_ClientBranch}\n')
    # file.write(f'.JY_ClientHeadSha={client_head_sha}\n')
    # file.write(f'.JY_ContentBranch={JY_ContentBranch}\n')
    # file.write(f'.JY_ContentHeadSha={content_head_sha}\n')
    # file.write('\n')
    # file.write('[/Script/VersionInfo]\n')
    # file.write(f'JY_AppVersion={JY_AppVersion}\n')
    # file.write(f'JY_ResVersion={JY_ResVersion}\n')
    file.write('[/Script/JYFramework.JYGameVersionSetting]\n')
    file.write(f'MajorVersion={JY_MajorVersion}\n')
    file.write(f'MinorVersion={JY_MinorVersion}\n')
    file.write(f'FixVersion={JY_FixVersion}\n')
    file.write(f'BuildNo={JY_BuildNo}\n')
    file.write(f'EngineSHA={engine_head_sha}\n')
    file.write(f'ClientSHA={client_head_sha}\n')
    file.write(f'ContentSHA={content_head_sha}\n')
    file.write(f'EngineBranch={JY_EngineBranch}\n')
    file.write(f'ClientBranch={JY_ClientBranch}\n')
    file.write(f'ContentBranch={JY_ContentBranch}\n')
    file.write(f'EngineTag={engine_tag}\n')
    file.write(f'ClientTag={client_tag}\n')
    file.write(f'ContentTag={content_tag}\n')

with open(os.path.join(JY_ClientContentDir, 'Version.txt'), 'w') as file:
    file.write(JY_AppVersion)

with open(os.path.join(JY_ClientSrcDir, 'Config', 'DefaultEngine.ini'), 'a') as file:
    file.write('\n')
    file.write('[/Script/AndroidRuntimeSettings.AndroidRuntimeSettings]\n')
    file.write(f'VersionDisplayName={JY_AppVersion}\n')
    file.write(f'StoreVersion={JY_VersionBuildNo}\n')
    file.write('\n')
    file.write('[/Script/IOSRuntimeSettings.IOSRuntimeSettings]\n')
    file.write(f'VersionInfo={JY_AppVersion}\n')