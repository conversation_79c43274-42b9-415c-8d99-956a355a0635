@echo off
echo Cleaning JYGitVersionUtils related build files...

REM Delete JYGitVersionUtils compiled objects
del /Q "H:\SRC\UnrealEngine\Games\JYGame\Intermediate\Build\Win64\x64\UnrealEditor\Development\JYFramework\JYGitVersionUtils*" 2>nul
del /Q "H:\SRC\UnrealEngine\Games\JYGame\Intermediate\Build\Win64\x64\UnrealEditor\DebugGame\JYFramework\JYGitVersionUtils*" 2>nul

REM Delete JsWatermarkWidget compiled objects (depends on JYGitVersionUtils)
del /Q "H:\SRC\UnrealEngine\Games\JYGame\Intermediate\Build\Win64\x64\UnrealEditor\Development\JYFrontEnd\JsWatermarkWidget*" 2>nul
del /Q "H:\SRC\UnrealEngine\Games\JYGame\Intermediate\Build\Win64\x64\UnrealEditor\DebugGame\JYFrontEnd\JsWatermarkWidget*" 2>nul

REM Delete UHT generated files (will be regenerated)
del /Q "H:\SRC\UnrealEngine\Games\JYGame\Intermediate\Build\Win64\UnrealEditor\Inc\JYFramework\UHT\JYGitVersionUtils*" 2>nul

echo Done! You can now compile the project and only the modified files will be rebuilt.
pause
