#include "JYGitVersionUtils.h"
#include "Engine/Engine.h"

#if WITH_EDITOR

/**
 * 简单的Git版本工具测试函数
 * 可以在编辑器中通过控制台命令调用
 */
void TestJYGitVersionUtils()
{
	UE_LOG(LogTemp, Warning, TEXT("=== Testing JYGitVersionUtils ==="));
	
	// 测试Git可用性
	if (!UJYGitVersionUtils::IsGitAvailable())
	{
		UE_LOG(LogTemp, Error, TEXT("Git is not available on this system"));
		return;
	}

	// 测试本地Git功能开关
	bool bLocalGitEnabled = UJYGitVersionUtils::IsLocalGitVersionEnabled();
	UE_LOG(LogTemp, Warning, TEXT("Local Git Version Enabled: %s"), bLocalGitEnabled ? TEXT("Yes") : TEXT("No"));
	
	// 获取完整的Git版本信息（三个仓库）
	FJYGitVersionInfo GitInfo = UJYGitVersionUtils::GetGitVersionInfo();

	UE_LOG(LogTemp, Warning, TEXT("Git Version Info (Three Repositories):"));
	UE_LOG(LogTemp, Warning, TEXT("  Overall Valid: %s"), GitInfo.bIsValid ? TEXT("Yes") : TEXT("No"));

	UE_LOG(LogTemp, Warning, TEXT("  Engine Repository:"));
	UE_LOG(LogTemp, Warning, TEXT("    Valid: %s"), GitInfo.EngineInfo.bIsValid ? TEXT("Yes") : TEXT("No"));
	UE_LOG(LogTemp, Warning, TEXT("    Branch: %s"), *GitInfo.EngineInfo.BranchName);
	UE_LOG(LogTemp, Warning, TEXT("    Short SHA: %s"), *GitInfo.EngineInfo.ShortSHA);
	UE_LOG(LogTemp, Warning, TEXT("    Tag: %s"), *GitInfo.EngineInfo.CurrentTag);
	UE_LOG(LogTemp, Warning, TEXT("    Has Changes: %s"), GitInfo.EngineInfo.bHasUncommittedChanges ? TEXT("Yes") : TEXT("No"));

	UE_LOG(LogTemp, Warning, TEXT("  Client Repository:"));
	UE_LOG(LogTemp, Warning, TEXT("    Valid: %s"), GitInfo.ClientInfo.bIsValid ? TEXT("Yes") : TEXT("No"));
	UE_LOG(LogTemp, Warning, TEXT("    Branch: %s"), *GitInfo.ClientInfo.BranchName);
	UE_LOG(LogTemp, Warning, TEXT("    Short SHA: %s"), *GitInfo.ClientInfo.ShortSHA);
	UE_LOG(LogTemp, Warning, TEXT("    Tag: %s"), *GitInfo.ClientInfo.CurrentTag);
	UE_LOG(LogTemp, Warning, TEXT("    Has Changes: %s"), GitInfo.ClientInfo.bHasUncommittedChanges ? TEXT("Yes") : TEXT("No"));

	UE_LOG(LogTemp, Warning, TEXT("  Content Repository:"));
	UE_LOG(LogTemp, Warning, TEXT("    Valid: %s"), GitInfo.ContentInfo.bIsValid ? TEXT("Yes") : TEXT("No"));
	UE_LOG(LogTemp, Warning, TEXT("    Branch: %s"), *GitInfo.ContentInfo.BranchName);
	UE_LOG(LogTemp, Warning, TEXT("    Short SHA: %s"), *GitInfo.ContentInfo.ShortSHA);
	UE_LOG(LogTemp, Warning, TEXT("    Tag: %s"), *GitInfo.ContentInfo.CurrentTag);
	UE_LOG(LogTemp, Warning, TEXT("    Has Changes: %s"), GitInfo.ContentInfo.bHasUncommittedChanges ? TEXT("Yes") : TEXT("No"));
	
	// 测试单独的函数
	FString CurrentBranch = UJYGitVersionUtils::GetCurrentBranch();
	FString CurrentSHA = UJYGitVersionUtils::GetCurrentCommitSHA();
	FString ShortSHA = UJYGitVersionUtils::GetCurrentCommitSHA(TEXT(""), true);
	FString CurrentTag = UJYGitVersionUtils::GetCurrentTag();
	bool HasChanges = UJYGitVersionUtils::HasUncommittedChanges();

	UE_LOG(LogTemp, Warning, TEXT("Individual Function Tests:"));
	UE_LOG(LogTemp, Warning, TEXT("  GetCurrentBranch(): %s"), *CurrentBranch);
	UE_LOG(LogTemp, Warning, TEXT("  GetCurrentCommitSHA(): %s"), *CurrentSHA);
	UE_LOG(LogTemp, Warning, TEXT("  GetCurrentCommitSHA(short): %s"), *ShortSHA);
	UE_LOG(LogTemp, Warning, TEXT("  GetCurrentTag(): %s"), *CurrentTag);
	UE_LOG(LogTemp, Warning, TEXT("  HasUncommittedChanges(): %s"), HasChanges ? TEXT("Yes") : TEXT("No"));
	
	// 测试最后提交信息
	FString Author, Timestamp, Message;
	bool bSuccess = UJYGitVersionUtils::GetLastCommitInfo(TEXT(""), Author, Timestamp, Message);
	UE_LOG(LogTemp, Warning, TEXT("GetLastCommitInfo Success: %s"), bSuccess ? TEXT("Yes") : TEXT("No"));
	if (bSuccess)
	{
		UE_LOG(LogTemp, Warning, TEXT("  Author: %s"), *Author);
		UE_LOG(LogTemp, Warning, TEXT("  Timestamp: %s"), *Timestamp);
		UE_LOG(LogTemp, Warning, TEXT("  Message: %s"), *Message);
	}
	
	UE_LOG(LogTemp, Warning, TEXT("=== Test Complete ==="));
}

// 注册控制台命令
static FAutoConsoleCommand TestGitVersionCommand(
	TEXT("JY.TestGitVersion"),
	TEXT("Test the JYGitVersionUtils functionality"),
	FConsoleCommandDelegate::CreateStatic(TestJYGitVersionUtils)
);

#endif // WITH_EDITOR
