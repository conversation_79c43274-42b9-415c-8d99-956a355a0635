#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "JYGitVersionUtils.generated.h"

// Force regeneration - v6

/**
 * 单个仓库的Git版本信息结构体
 */
USTRUCT(BlueprintType)
struct JYFRAMEWORK_API FJYSingleRepoGitInfo
{
	GENERATED_USTRUCT_BODY()

	/** 当前分支名 */
	UPROPERTY(BlueprintReadOnly)
	FString BranchName;

	/** 当前提交的SHA值 */
	UPROPERTY(BlueprintReadOnly)
	FString CommitSHA;

	/** 短SHA值（前7位） */
	UPROPERTY(BlueprintReadOnly)
	FString ShortSHA;

	/** 是否有未提交的更改 */
	UPROPERTY(BlueprintReadOnly)
	bool bHasUncommittedChanges;

	/** 最后提交的时间戳 */
	UPROPERTY(BlueprintReadOnly)
	FString LastCommitTimestamp;

	/** 最后提交的作者 */
	UPROPERTY(BlueprintReadOnly)
	FString LastCommitAuthor;

	/** 最后提交的消息 */
	UPROPERTY(BlueprintReadOnly)
	FString LastCommitMessage;

	/** 当前分支所在的Tag（如果有） */
	UPROPERTY(BlueprintReadOnly)
	FString CurrentTag;

	/** 是否成功获取到git信息 */
	UPROPERTY(BlueprintReadOnly)
	bool bIsValid;

	FJYSingleRepoGitInfo()
	{
		BranchName = TEXT("Unknown");
		CommitSHA = TEXT("Unknown");
		ShortSHA = TEXT("Unknown");
		bHasUncommittedChanges = false;
		LastCommitTimestamp = TEXT("Unknown");
		LastCommitAuthor = TEXT("Unknown");
		LastCommitMessage = TEXT("Unknown");
		CurrentTag = TEXT("Unknown");
		bIsValid = false;
	}
};

/**
 * 三个仓库的Git版本信息结构体
 */
USTRUCT(BlueprintType)
struct JYFRAMEWORK_API FJYGitVersionInfo
{
	GENERATED_USTRUCT_BODY()

	/** Engine仓库信息 */
	UPROPERTY(BlueprintReadOnly)
	FJYSingleRepoGitInfo EngineInfo;

	/** Client仓库信息 */
	UPROPERTY(BlueprintReadOnly)
	FJYSingleRepoGitInfo ClientInfo;

	/** Content仓库信息 */
	UPROPERTY(BlueprintReadOnly)
	FJYSingleRepoGitInfo ContentInfo;

	/** 是否至少有一个仓库的信息有效 */
	UPROPERTY(BlueprintReadOnly)
	bool bIsValid;

	FJYGitVersionInfo()
	{
		bIsValid = false;
	}
};

/**
 * Git版本信息获取工具类
 * 用于在运行时直接从git仓库获取版本信息
 */
UCLASS()
class JYFRAMEWORK_API UJYGitVersionUtils : public UBlueprintFunctionLibrary
{
	GENERATED_UCLASS_BODY()

public:
	/**
	 * 获取三个仓库的Git版本信息（Engine、Client、Content）
	 * @return 包含三个仓库信息的结构体
	 */
	UFUNCTION(BlueprintCallable, Category = "JY|Git Version")
	static FJYGitVersionInfo GetGitVersionInfo();

	/**
	 * 获取指定路径的单个仓库Git版本信息
	 * @param RepositoryPath Git仓库路径，如果为空则使用项目根目录
	 * @return 单个仓库的Git版本信息结构体
	 */
	UFUNCTION(BlueprintCallable, Category = "JY|Git Version")
	static FJYSingleRepoGitInfo GetSingleRepoGitInfo(const FString& RepositoryPath = TEXT(""));

	/**
	 * 获取当前分支名
	 * @param RepositoryPath Git仓库路径
	 * @return 分支名
	 */
	UFUNCTION(BlueprintCallable, Category = "JY|Git Version")
	static FString GetCurrentBranch(const FString& RepositoryPath = TEXT(""));

	/**
	 * 获取当前提交的SHA值
	 * @param RepositoryPath Git仓库路径
	 * @param bShortSHA 是否返回短SHA（前7位）
	 * @return SHA值
	 */
	UFUNCTION(BlueprintCallable, Category = "JY|Git Version")
	static FString GetCurrentCommitSHA(const FString& RepositoryPath = TEXT(""), bool bShortSHA = false);

	/**
	 * 检查是否有未提交的更改
	 * @param RepositoryPath Git仓库路径
	 * @return 是否有未提交的更改
	 */
	UFUNCTION(BlueprintCallable, Category = "JY|Git Version")
	static bool HasUncommittedChanges(const FString& RepositoryPath = TEXT(""));

	/**
	 * 获取最后提交的信息
	 * @param RepositoryPath Git仓库路径
	 * @param OutAuthor 输出作者名
	 * @param OutTimestamp 输出时间戳
	 * @param OutMessage 输出提交消息
	 * @return 是否成功获取
	 */
	UFUNCTION(BlueprintCallable, Category = "JY|Git Version")
	static bool GetLastCommitInfo(const FString& RepositoryPath, FString& OutAuthor, FString& OutTimestamp, FString& OutMessage);

	/**
	 * 检查Git是否可用
	 * @return Git是否可用
	 */
	UFUNCTION(BlueprintCallable, Category = "JY|Git Version")
	static bool IsGitAvailable();

	/**
	 * 检查是否启用了本地Git版本信息功能
	 * @return 是否启用
	 */
	UFUNCTION(BlueprintCallable, Category = "JY|Git Version")
	static bool IsLocalGitVersionEnabled();

	/**
	 * 判断当前是否为Binaries环境
	 * @return 是否为Binaries环境
	 */
	UFUNCTION(BlueprintCallable, Category = "JY|Git Version")
	static bool IsBinariesEnvironment();

	/**
	 * 获取当前分支所在的Tag
	 * @param RepositoryPath Git仓库路径
	 * @return Tag名称，如果没有则返回"Unknown"
	 */
	UFUNCTION(BlueprintCallable, Category = "JY|Git Version")
	static FString GetCurrentTag(const FString& RepositoryPath = TEXT(""));

private:
	/**
	 * 执行Git命令并返回结果
	 * @param GitCommand Git命令（不包含"git"前缀）
	 * @param RepositoryPath 仓库路径
	 * @param OutResult 输出结果
	 * @param OutError 输出错误信息
	 * @return 是否执行成功
	 */
	static bool ExecuteGitCommand(const FString& GitCommand, const FString& RepositoryPath, FString& OutResult, FString& OutError);

	/**
	 * 获取规范化的仓库路径
	 * @param RepositoryPath 输入的仓库路径
	 * @return 规范化后的路径
	 */
	static FString GetNormalizedRepositoryPath(const FString& RepositoryPath);

	/**
	 * 判断当前是否为Binaries环境
	 * @return 是否为Binaries环境
	 */
	static bool IsBinariesEnvironment();

	/**
	 * 获取Engine仓库路径
	 * @param bIsBinaries 是否为Binaries环境
	 * @return Engine仓库路径
	 */
	static FString GetEngineRepositoryPath(bool bIsBinaries = false);

	/**
	 * 获取Client仓库路径
	 * @param bIsBinaries 是否为Binaries环境
	 * @return Client仓库路径
	 */
	static FString GetClientRepositoryPath(bool bIsBinaries = false);

	/**
	 * 获取Content仓库路径
	 * @param bIsBinaries 是否为Binaries环境（Content仓库SRC和BIN相同）
	 * @return Content仓库路径
	 */
	static FString GetContentRepositoryPath(bool bIsBinaries = false);
};
