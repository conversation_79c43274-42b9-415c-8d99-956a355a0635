@echo off
echo Syncing modified files from F:\JyGame to H:\SRC\UnrealEngine\Games\JYGame...

REM Sync JYGitVersionUtils files
echo Syncing JYGitVersionUtils files...
copy "F:\JyGame\Source\JYFramework\Utilities\JYGitVersionUtils.h" "H:\SRC\UnrealEngine\Games\JYGame\Source\JYFramework\Utilities\JYGitVersionUtils.h"
copy "F:\JyGame\Source\JYFramework\Utilities\JYGitVersionUtils.cpp" "H:\SRC\UnrealEngine\Games\JYGame\Source\JYFramework\Utilities\JYGitVersionUtils.cpp"
copy "F:\JyGame\Source\JYFramework\Utilities\JYGitVersionUtilsTest.cpp" "H:\SRC\UnrealEngine\Games\JYGame\Source\JYFramework\Utilities\JYGitVersionUtilsTest.cpp"

REM Sync JsWatermarkWidget files
echo Syncing JsWatermarkWidget files...
copy "F:\JyGame\Source\JYFrontEnd\UI\Widget\JsWatermarkWidget.h" "H:\SRC\UnrealEngine\Games\JYGame\Source\JYFrontEnd\UI\Widget\JsWatermarkWidget.h"
copy "F:\JyGame\Source\JYFrontEnd\UI\Widget\JsWatermarkWidget.cpp" "H:\SRC\UnrealEngine\Games\JYGame\Source\JYFrontEnd\UI\Widget\JsWatermarkWidget.cpp"

REM Clean build cache
echo Cleaning build cache...
rmdir /s /q "H:\SRC\UnrealEngine\Games\JYGame\Intermediate\Build\Win64\x64\UnrealEditor\Development\JYFramework" 2>nul
rmdir /s /q "H:\SRC\UnrealEngine\Games\JYGame\Intermediate\Build\Win64\x64\UnrealEditor\Development\JYFrontEnd" 2>nul
rmdir /s /q "H:\SRC\UnrealEngine\Games\JYGame\Intermediate\Build\Win64\UnrealEditor\Inc\JYFramework" 2>nul
rmdir /s /q "H:\SRC\UnrealEngine\Games\JYGame\Intermediate\Build\Win64\UnrealEditor\Inc\JYFrontEnd" 2>nul

echo Sync completed! You can now compile the project on H drive.
pause
